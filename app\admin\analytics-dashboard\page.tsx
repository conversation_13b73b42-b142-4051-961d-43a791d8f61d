"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Activity,
  Download,
  RefreshCw,
  Calendar,
  Eye,
  Clock,
  Server,
  Zap,
  AlertTriangle,
  CheckCircle,
  Globe,
  Database,
} from "lucide-react"
import { motion } from "framer-motion"
import { toast } from "@/lib/toast-utils"

interface RealTimeMetrics {
  lastHour: {
    activeUsers: number
    quizAttempts: number
    notifications: number
    apiRequests: number
  }
  today: {
    activeUsers: number
    quizAttempts: number
  }
  timestamp: string
}

interface AnalyticsData {
  overview: {
    totalUsers: number
    activeUsers: number
    totalQuizzes: number
    totalAttempts: number
    totalQuestions: number
    averageScore: number
    completionRate: number
    totalNotifications: number
    totalFiles: number
    totalApiKeys: number
  }
  engagement: {
    dailyActiveUsers: Array<{ date: string; count: number }>
    weeklyActiveUsers: Array<{ week: string; count: number }>
    userRetention: {
      day1: number
      day7: number
      day30: number
    }
    sessionDuration: {
      average: number
      median: number
    }
  }
  quizMetrics: {
    topPerformingQuizzes: Array<{
      id: string
      title: string
      attempts: number
      averageScore: number
      completionRate: number
      difficulty: string
      type: string
    }>
    quizDifficultyAnalysis: Array<{
      difficulty: string
      count: number
      averageScore: number
      completionRate: number
    }>
  }
  systemMetrics: {
    apiUsage: {
      totalRequests: number
      averageResponseTime: number
      errorRate: number
      topEndpoints: Array<{ endpoint: string; requests: number; avgTime: number }>
    }
    fileStorage: {
      totalFiles: number
      totalSize: number
      uploadTrends: Array<{ date: string; uploads: number; size: number }>
    }
    notifications: {
      totalSent: number
      deliveryRate: number
      readRate: number
      clickRate: number
      topCategories: Array<{ category: string; count: number; engagement: number }>
    }
  }
}

// Placeholder Chart Components (replace with actual chart library)
const LineChart = ({ data, title }: { data: any[]; title: string }) => (
  <div className="h-64 bg-muted rounded-lg flex items-center justify-center">
    <div className="text-center">
      <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
      <p className="text-sm text-muted-foreground">{title} Chart</p>
      <p className="text-xs text-muted-foreground">{data.length} data points</p>
    </div>
  </div>
)

const BarChart = ({ data, title }: { data: any[]; title: string }) => (
  <div className="h-64 bg-muted rounded-lg flex items-center justify-center">
    <div className="text-center">
      <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
      <p className="text-sm text-muted-foreground">{title} Chart</p>
      <p className="text-xs text-muted-foreground">{data.length} data points</p>
    </div>
  </div>
)

const PieChart = ({ data, title }: { data: any[]; title: string }) => (
  <div className="h-64 bg-muted rounded-lg flex items-center justify-center">
    <div className="text-center">
      <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
      <p className="text-sm text-muted-foreground">{title} Chart</p>
      <p className="text-xs text-muted-foreground">{data.length} segments</p>
    </div>
  </div>
)

export default function AnalyticsDashboardPage() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [realTimeMetrics, setRealTimeMetrics] = useState<RealTimeMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState('30d')
  const [autoRefresh, setAutoRefresh] = useState(true)

  const loadAnalyticsData = useCallback(async (period: string = selectedPeriod) => {
    try {
      setIsRefreshing(true)
      
      const [overviewRes, engagementRes, quizzesRes, systemRes] = await Promise.all([
        fetch(`/api/analytics/overview?period=${period}`),
        fetch(`/api/analytics/engagement?period=${period}`),
        fetch(`/api/analytics/quizzes?period=${period}`),
        fetch(`/api/analytics/system?period=${period}`)
      ])

      if (overviewRes.ok && engagementRes.ok && quizzesRes.ok && systemRes.ok) {
        const [overview, engagement, quizzes, system] = await Promise.all([
          overviewRes.json(),
          engagementRes.json(),
          quizzesRes.json(),
          systemRes.json()
        ])

        setAnalyticsData({
          overview: overview.data.overview,
          engagement: engagement.data.engagement,
          quizMetrics: quizzes.data.quizMetrics,
          systemMetrics: system.data.systemMetrics
        })
      } else {
        toast.error('Failed to load analytics data')
      }
    } catch (error) {
      console.error('Error loading analytics:', error)
      toast.error('Failed to load analytics data')
    } finally {
      setIsLoading(false)
      setIsRefreshing(false)
    }
  }, [selectedPeriod])

  const loadRealTimeMetrics = useCallback(async () => {
    try {
      const response = await fetch('/api/analytics/realtime')
      if (response.ok) {
        const data = await response.json()
        setRealTimeMetrics(data.data)
      }
    } catch (error) {
      console.error('Error loading real-time metrics:', error)
    }
  }, [])

  useEffect(() => {
    loadAnalyticsData()
    loadRealTimeMetrics()
  }, [loadAnalyticsData, loadRealTimeMetrics])

  // Auto-refresh real-time metrics
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      loadRealTimeMetrics()
    }, 30000) // Refresh every 30 seconds

    return () => clearInterval(interval)
  }, [autoRefresh, loadRealTimeMetrics])

  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period)
    loadAnalyticsData(period)
  }

  const handleRefresh = () => {
    loadAnalyticsData()
    loadRealTimeMetrics()
  }



  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K'
    return num.toString()
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
        <div className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-2xl flex items-center justify-center mx-auto mb-6 backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent"></div>
              </div>
              <p className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent mb-2">
                Loading Analytics Dashboard...
              </p>
              <p className="text-muted-foreground">Gathering insights from your data</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
      <motion.div className="p-6 space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="relative"
        >
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10 rounded-2xl blur-3xl -z-10" />

          <div className="relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-white/20 dark:border-gray-800/20 shadow-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                  <BarChart3 className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Analytics Dashboard
                  </h1>
                  <p className="text-muted-foreground text-lg">
                    Comprehensive insights and performance metrics
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Select value={selectedPeriod} onValueChange={handlePeriodChange}>
                  <SelectTrigger className="w-40 glass bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm border-white/30 dark:border-gray-700/30">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7d">Last 7 days</SelectItem>
                    <SelectItem value="30d">Last 30 days</SelectItem>
                    <SelectItem value="90d">Last 90 days</SelectItem>
                    <SelectItem value="1y">Last year</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant="outline"
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                  className="glass hover:bg-white/20 dark:hover:bg-gray-800/20 border-white/20 dark:border-gray-700/20"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>

                <Button
                  variant="outline"
                  className="glass hover:bg-white/20 dark:hover:bg-gray-800/20 border-white/20 dark:border-gray-700/20"
                  onClick={() => setAutoRefresh(!autoRefresh)}
                >
                  <Activity className="h-4 w-4 mr-2" />
                  {autoRefresh ? 'Auto-refresh ON' : 'Auto-refresh OFF'}
                </Button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Real-time Metrics */}
        {realTimeMetrics && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-6"
          >
            <Card className="glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-xl hover:shadow-2xl transition-all duration-300 group">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                      {realTimeMetrics.lastHour.activeUsers}
                    </div>
                    <p className="text-sm text-muted-foreground font-medium">Active Users (1h)</p>
                  </div>
                  <div className="p-3 bg-gradient-to-br from-green-500/20 to-emerald-600/20 rounded-xl backdrop-blur-sm border border-white/20 dark:border-gray-700/20 group-hover:scale-110 transition-transform duration-300">
                    <Users className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-xl hover:shadow-2xl transition-all duration-300 group">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      {realTimeMetrics.lastHour.quizAttempts}
                    </div>
                    <p className="text-sm text-muted-foreground font-medium">Quiz Attempts (1h)</p>
                  </div>
                  <div className="p-3 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-xl backdrop-blur-sm border border-white/20 dark:border-gray-700/20 group-hover:scale-110 transition-transform duration-300">
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    {realTimeMetrics.lastHour.notifications}
                  </div>
                  <p className="text-sm text-muted-foreground">Notifications (1h)</p>
                </div>
                <Globe className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>


        </div>
      )}

      {/* Overview Cards */}
      {analyticsData && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {formatNumber(analyticsData.overview.totalUsers)}
                  </div>
                  <p className="text-sm text-muted-foreground">Total Users</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {formatNumber(analyticsData.overview.totalQuizzes)}
                  </div>
                  <p className="text-sm text-muted-foreground">Total Quizzes</p>
                </div>
                <BarChart3 className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {formatNumber(analyticsData.overview.totalAttempts)}
                  </div>
                  <p className="text-sm text-muted-foreground">Total Attempts</p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {analyticsData.overview.averageScore.toFixed(1)}%
                  </div>
                  <p className="text-sm text-muted-foreground">Avg Score</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {analyticsData.overview.completionRate.toFixed(1)}%
                  </div>
                  <p className="text-sm text-muted-foreground">Completion Rate</p>
                </div>
                <Activity className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Detailed Analytics */}
      {analyticsData && (
        <Tabs defaultValue="engagement" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="engagement">User Engagement</TabsTrigger>
            <TabsTrigger value="quizzes">Quiz Performance</TabsTrigger>
            <TabsTrigger value="system">System Health</TabsTrigger>
          </TabsList>

          {/* User Engagement Tab */}
          <TabsContent value="engagement" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Daily Active Users</CardTitle>
                  <CardDescription>
                    User activity over the selected period
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <LineChart 
                    data={analyticsData.engagement.dailyActiveUsers} 
                    title="Daily Active Users"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>User Retention</CardTitle>
                  <CardDescription>
                    Percentage of users returning after signup
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Day 1 Retention</span>
                      <Badge variant="outline">
                        {analyticsData.engagement.userRetention.day1.toFixed(1)}%
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Day 7 Retention</span>
                      <Badge variant="outline">
                        {analyticsData.engagement.userRetention.day7.toFixed(1)}%
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Day 30 Retention</span>
                      <Badge variant="outline">
                        {analyticsData.engagement.userRetention.day30.toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Session Duration</CardTitle>
                  <CardDescription>
                    Average time users spend on the platform
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Average Duration</span>
                      <Badge variant="outline">
                        {Math.round(analyticsData.engagement.sessionDuration.average / 60)} min
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Median Duration</span>
                      <Badge variant="outline">
                        {Math.round(analyticsData.engagement.sessionDuration.median / 60)} min
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Weekly Active Users</CardTitle>
                  <CardDescription>
                    Weekly user engagement trends
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <BarChart 
                    data={analyticsData.engagement.weeklyActiveUsers} 
                    title="Weekly Active Users"
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Quiz Performance Tab */}
          <TabsContent value="quizzes" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Top Performing Quizzes</CardTitle>
                  <CardDescription>
                    Quizzes with highest average scores
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsData.quizMetrics.topPerformingQuizzes.slice(0, 5).map((quiz, index) => (
                      <div key={quiz.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <div className="font-medium">{quiz.title}</div>
                          <div className="text-sm text-muted-foreground">
                            {quiz.attempts} attempts • {quiz.difficulty} difficulty
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{quiz.averageScore.toFixed(1)}%</div>
                          <div className="text-sm text-muted-foreground">
                            {quiz.completionRate.toFixed(1)}% completion
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Quiz Difficulty Analysis</CardTitle>
                  <CardDescription>
                    Performance breakdown by difficulty level
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <PieChart 
                    data={analyticsData.quizMetrics.quizDifficultyAnalysis} 
                    title="Quiz Difficulty Distribution"
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* System Health Tab */}
          <TabsContent value="system" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

              <Card>
                <CardHeader>
                  <CardTitle>File Storage</CardTitle>
                  <CardDescription>
                    Storage usage and upload trends
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Total Files</span>
                      <Badge variant="outline">
                        {formatNumber(analyticsData.systemMetrics.fileStorage.totalFiles)}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Storage Used</span>
                      <Badge variant="outline">
                        {formatBytes(analyticsData.systemMetrics.fileStorage.totalSize)}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Notifications</CardTitle>
                  <CardDescription>
                    Notification delivery and engagement
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Total Sent</span>
                      <Badge variant="outline">
                        {formatNumber(analyticsData.systemMetrics.notifications.totalSent)}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Delivery Rate</span>
                      <Badge variant="outline">
                        {analyticsData.systemMetrics.notifications.deliveryRate.toFixed(1)}%
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Read Rate</span>
                      <Badge variant="outline">
                        {analyticsData.systemMetrics.notifications.readRate.toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>


        </Tabs>
      )}
    </motion.div>
  )
}
