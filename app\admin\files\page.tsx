"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { FileUpload } from "@/components/ui/file-upload"
import { FileManager } from "@/components/ui/file-manager"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"

import { 
  Upload, 
  FolderOpen, 
  Image, 
  FileText, 
  Settings,
  Info
} from "lucide-react"
import { toast } from "@/lib/toast-utils"
import { DeleteConfirmationDialog } from "@/components/ui/confirmation-dialog"

interface UploadedFile {
  id: string
  filename: string
  originalName: string
  url: string
  size: number
  mimeType: string
  uploadType: string
}

interface FileStats {
  overview: {
    totalFiles: number
    totalSize: string
    totalSizeBytes: number
    averageFileSize: string
    averageFileSizeBytes: number
  }
  filesByType: Array<{
    type: string
    count: number
    size: string
    sizeBytes: number
  }>
  filesByFolder: Array<{
    folder: string
    count: number
    size: string
    sizeBytes: number
  }>
  recentUploads: UploadedFile[]
  period: string
}

export default function FilesPage() {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [stats, setStats] = useState<FileStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [uploadTypeFilter, setUploadTypeFilter] = useState<string>('')
  const [folderFilter, setFolderFilter] = useState<string>('')
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [sortBy, setSortBy] = useState<'name' | 'size' | 'date' | 'type'>('date')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [period, setPeriod] = useState<'7d' | '30d' | '90d' | 'all'>('30d')
  const [showSettingsDialog, setShowSettingsDialog] = useState(false)
  const [storageSettings, setStorageSettings] = useState({
    maxFileSize: 10,
    allowedFileTypes: ['pdf', 'doc', 'docx', 'txt', 'jpg', 'png', 'gif'],
    autoDeleteAfterDays: 365,
    enableCompression: true,
    enableThumbnails: true
  })
  useEffect(() => {
    fetchFiles()
    fetchStats()
  }, [currentPage, uploadTypeFilter, folderFilter, searchQuery, sortBy, sortOrder, period])

  const fetchFiles = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        sortBy,
        sortOrder,
        ...(uploadTypeFilter && { uploadType: uploadTypeFilter }),
        ...(folderFilter && { folder: folderFilter }),
        ...(searchQuery && { search: searchQuery })
      })

      const response = await fetch(`/api/admin/files?${params}`)
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: { message: 'Unknown error' } }))
        throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      setUploadedFiles(data.data.files)
      setTotalPages(data.data.pagination.totalPages)
    } catch (error: any) {
      console.error('Error fetching files:', error)
      toast.error(`Failed to load files: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      console.log('Fetching stats for period:', period)
      const response = await fetch(`/api/admin/files/stats?period=${period}`)
      console.log('Stats response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: { message: 'Unknown error' } }))
        console.error('Stats API error:', errorData)
        throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.log('Stats data received:', data)
      setStats(data.data)
    } catch (error: any) {
      console.error('Error fetching stats:', error)
      toast.error(`Failed to load statistics: ${error.message}`)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await Promise.all([fetchFiles(), fetchStats()])
    setRefreshing(false)
    toast.success('Data refreshed')
  }

  const handleFileUpload = (files: UploadedFile[]) => {
    setUploadedFiles(prev => [...prev, ...files])
    toast.success(`${files.length} file(s) uploaded successfully!`)
  }

  const handleFileDelete = async (fileId: string) => {
    try {
      const response = await fetch(`/api/admin/files/${fileId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || 'Failed to delete file')
      }

      await fetchFiles()
      await fetchStats()
      toast.success('File deleted successfully')
    } catch (error: any) {
      console.error('Error deleting file:', error)
      toast.error(error.message || 'Failed to delete file')
    }
  }

  const handleFileSelect = (file: UploadedFile) => {
    toast.success(`Selected: ${file.originalName}`)
    console.log('Selected file:', file)
  }

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">File Management</h1>
          <p className="text-muted-foreground mt-1">
            Upload and manage files using Bunny CDN storage
          </p>
        </div>
        <div className="flex items-center gap-2">
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value as '7d' | '30d' | '90d' | 'all')}
            className="px-3 py-1 border rounded-md text-sm"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="all">All time</option>
          </select>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <Upload className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSettingsDialog(true)}
          >
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Configuration Info */}
      <Card className="border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-900/20">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5" />
            <div className="space-y-2">
              <h4 className="font-semibold text-blue-900 dark:text-blue-100">
                Bunny CDN Storage Configuration
              </h4>
              <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                <p>• Files are uploaded to Bunny CDN for fast global delivery</p>
                <p>• Images are automatically optimized for web performance</p>
                <p>• Maximum file size: 10MB for documents, 5MB for images</p>
                <p>• Supported formats: Images (JPG, PNG, GIF, WebP), Documents (PDF, DOC, DOCX, TXT)</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    stats?.overview.totalFiles.toLocaleString() || '0'
                  )}
                </div>
                <p className="text-sm text-muted-foreground">Total Files</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    stats?.overview.totalSize || '0 B'
                  )}
                </div>
                <p className="text-sm text-muted-foreground">Total Size</p>
              </div>
              <FolderOpen className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    stats?.overview.averageFileSize || '0 B'
                  )}
                </div>
                <p className="text-sm text-muted-foreground">Avg File Size</p>
              </div>
              <Upload className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                  ) : (
                    stats?.filesByType.length.toLocaleString() || '0'
                  )}
                </div>
                <p className="text-sm text-muted-foreground">File Types</p>
              </div>
              <Image className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* File Management Tabs */}
      <Tabs defaultValue="upload" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="upload" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Upload Files
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            File History
          </TabsTrigger>
          <TabsTrigger value="manage" className="flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            File Manager
          </TabsTrigger>
          <TabsTrigger value="gallery" className="flex items-center gap-2">
            <Image className="h-4 w-4" />
            Image Gallery
          </TabsTrigger>
        </TabsList>

        {/* Upload Tab */}
        <TabsContent value="upload" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Image Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Image className="h-5 w-5" />
                  Image Upload
                </CardTitle>
                <CardDescription>
                  Upload images for quizzes, thumbnails, and content
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FileUpload
                  onUpload={handleFileUpload}
                  acceptedTypes={['image/*']}
                  maxFiles={10}
                  maxSize={5 * 1024 * 1024} // 5MB
                  uploadType="image"
                  folder="images"
                  multiple={true}
                />
              </CardContent>
            </Card>

            {/* Document Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Document Upload
                </CardTitle>
                <CardDescription>
                  Upload PDFs, documents, and other files
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FileUpload
                  onUpload={handleFileUpload}
                  acceptedTypes={[
                    'application/pdf',
                    'text/plain',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'application/vnd.openxmlformats-officedocument.presentationml.presentation'
                  ]}
                  maxFiles={5}
                  maxSize={10 * 1024 * 1024} // 10MB
                  uploadType="document"
                  folder="documents"
                  multiple={true}
                />
              </CardContent>
            </Card>
          </div>

          {/* General Upload */}
          <Card>
            <CardHeader>
              <CardTitle>General File Upload</CardTitle>
              <CardDescription>
                Upload any supported file type to the general uploads folder
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FileUpload
                onUpload={handleFileUpload}
                acceptedTypes={[
                  'image/*',
                  'application/pdf',
                  'text/plain',
                  '.doc',
                  '.docx',
                  '.xlsx',
                  '.pptx'
                ]}
                maxFiles={20}
                maxSize={10 * 1024 * 1024} // 10MB
                uploadType="general"
                folder="uploads"
                multiple={true}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* File History Tab */}
        <TabsContent value="history" className="space-y-6">
          <div className="space-y-6">
            {/* Filters */}
            <div className="flex items-center gap-4">
              <input
                type="text"
                placeholder="Search files..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="px-3 py-2 border rounded-md flex-1 max-w-sm"
              />
              <select
                value={uploadTypeFilter}
                onChange={(e) => setUploadTypeFilter(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="">All Types</option>
                <option value="image">Images</option>
                <option value="document">Documents</option>
                <option value="general">General</option>
              </select>
              <select
                value={folderFilter}
                onChange={(e) => setFolderFilter(e.target.value)}
                className="px-3 py-2 border rounded-md"
              >
                <option value="">All Folders</option>
                <option value="uploads">Uploads</option>
                <option value="images">Images</option>
                <option value="documents">Documents</option>
              </select>
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [newSortBy, newSortOrder] = e.target.value.split('-') as [typeof sortBy, typeof sortOrder]
                  setSortBy(newSortBy)
                  setSortOrder(newSortOrder)
                }}
                className="px-3 py-2 border rounded-md"
              >
                <option value="date-desc">Newest First</option>
                <option value="date-asc">Oldest First</option>
                <option value="name-asc">Name A-Z</option>
                <option value="name-desc">Name Z-A</option>
                <option value="size-desc">Largest First</option>
                <option value="size-asc">Smallest First</option>
              </select>
            </div>

            {/* File List */}
            <Card>
              <CardHeader>
                <CardTitle>File History</CardTitle>
                <CardDescription>
                  All uploaded files with management options
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex items-center justify-between p-4 border rounded-lg animate-pulse">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-muted rounded"></div>
                          <div className="flex-1">
                            <div className="h-4 bg-muted rounded w-48 mb-2"></div>
                            <div className="h-3 bg-muted rounded w-32"></div>
                          </div>
                        </div>
                        <div className="h-6 w-20 bg-muted rounded"></div>
                      </div>
                    ))}
                  </div>
                ) : uploadedFiles.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No files found</h3>
                    <p className="text-muted-foreground">
                      No files match your current filters.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {uploadedFiles.map((file) => (
                      <div key={file.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50">
                        <div className="flex items-center gap-4">
                          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                            file.mimeType.startsWith('image/') ? 'bg-green-100 text-green-600' :
                            file.mimeType.includes('pdf') || file.mimeType.includes('document') ? 'bg-blue-100 text-blue-600' :
                            'bg-gray-100 text-gray-600'
                          }`}>
                            {file.mimeType.startsWith('image/') && <Image className="h-6 w-6" />}
                            {(file.mimeType.includes('pdf') || file.mimeType.includes('document')) && <FileText className="h-6 w-6" />}
                            {!file.mimeType.startsWith('image/') && !file.mimeType.includes('pdf') && !file.mimeType.includes('document') && <FolderOpen className="h-6 w-6" />}
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium">{file.originalName}</h4>
                            <p className="text-sm text-muted-foreground">
                              {(file.size / 1024 / 1024).toFixed(2)} MB • {file.uploadType}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleFileSelect(file)}
                          >
                            <Upload className="h-4 w-4 mr-2" />
                            Select
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleFileDelete(file.id)}
                          >
                            <FileText className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Pagination */}
                {!loading && uploadedFiles.length > 0 && totalPages > 1 && (
                  <div className="flex items-center justify-center gap-2 mt-6">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <span className="text-sm text-muted-foreground">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* File Manager Tab */}
        <TabsContent value="manage">
          <Card>
            <CardHeader>
              <CardTitle>File Manager</CardTitle>
              <CardDescription>
                Browse, manage, and organize your uploaded files
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FileManager
                onFileSelect={handleFileSelect}
                onFileDelete={handleFileDelete}
                selectable={true}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Image Gallery Tab */}
        <TabsContent value="gallery">
          <Card>
            <CardHeader>
              <CardTitle>Image Gallery</CardTitle>
              <CardDescription>
                Browse and select from uploaded images
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FileManager
                onFileSelect={handleFileSelect}
                onFileDelete={handleFileDelete}
                selectable={true}
                uploadType="image"
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Recent Uploads */}
      {uploadedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Uploads</CardTitle>
            <CardDescription>
              Files uploaded in this session
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {uploadedFiles.slice(-6).map((file) => (
                <div
                  key={file.id}
                  className="flex items-center gap-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex-shrink-0">
                    {file.mimeType.startsWith('image/') ? (
                      <img
                        src={file.url}
                        alt={file.originalName}
                        className="w-12 h-12 object-cover rounded"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-muted rounded flex items-center justify-center">
                        <FileText className="h-6 w-6 text-muted-foreground" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-sm truncate">
                      {file.originalName}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {(file.size / 1024).toFixed(1)} KB • {file.uploadType}
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => window.open(file.url, '_blank')}
                  >
                    View
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Usage Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">{uploadedFiles.length}</div>
                <p className="text-sm text-muted-foreground">Files Uploaded</p>
              </div>
              <Upload className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {(uploadedFiles.reduce((sum, f) => sum + f.size, 0) / (1024 * 1024)).toFixed(1)}MB
                </div>
                <p className="text-sm text-muted-foreground">Total Size</p>
              </div>
              <FolderOpen className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">
                  {uploadedFiles.filter(f => f.mimeType.startsWith('image/')).length}
                </div>
                <p className="text-sm text-muted-foreground">Images</p>
              </div>
              <Image className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Storage Settings Dialog */}
      <Dialog open={showSettingsDialog} onOpenChange={setShowSettingsDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Storage Settings</DialogTitle>
            <DialogDescription>
              Configure file upload and storage settings
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="max-file-size">Max File Size (MB)</Label>
                <Input
                  id="max-file-size"
                  type="number"
                  value={storageSettings.maxFileSize}
                  onChange={(e) => setStorageSettings(prev => ({
                    ...prev,
                    maxFileSize: parseInt(e.target.value) || 10
                  }))}
                  min="1"
                  max="100"
                />
              </div>
              <div>
                <Label htmlFor="auto-delete">Auto Delete After (Days)</Label>
                <Input
                  id="auto-delete"
                  type="number"
                  value={storageSettings.autoDeleteAfterDays}
                  onChange={(e) => setStorageSettings(prev => ({
                    ...prev,
                    autoDeleteAfterDays: parseInt(e.target.value) || 365
                  }))}
                  min="1"
                  max="3650"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="allowed-types">Allowed File Types</Label>
              <Input
                id="allowed-types"
                value={storageSettings.allowedFileTypes.join(', ')}
                onChange={(e) => setStorageSettings(prev => ({
                  ...prev,
                  allowedFileTypes: e.target.value.split(',').map(type => type.trim())
                }))}
                placeholder="pdf, doc, docx, txt, jpg, png"
              />
              <p className="text-sm text-muted-foreground mt-1">
                Separate file extensions with commas
              </p>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="enable-compression">Enable Compression</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically compress images to save storage space
                  </p>
                </div>
                <Switch
                  id="enable-compression"
                  checked={storageSettings.enableCompression}
                  onCheckedChange={(checked) => setStorageSettings(prev => ({
                    ...prev,
                    enableCompression: checked
                  }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="enable-thumbnails">Generate Thumbnails</Label>
                  <p className="text-sm text-muted-foreground">
                    Create thumbnail previews for images and documents
                  </p>
                </div>
                <Switch
                  id="enable-thumbnails"
                  checked={storageSettings.enableThumbnails}
                  onCheckedChange={(checked) => setStorageSettings(prev => ({
                    ...prev,
                    enableThumbnails: checked
                  }))}
                />
              </div>
            </div>

            <div className="flex justify-end gap-2 pt-4 border-t">
              <Button variant="outline" onClick={() => setShowSettingsDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => {
                toast.success('Storage settings updated successfully')
                setShowSettingsDialog(false)
              }}>
                Save Settings
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
