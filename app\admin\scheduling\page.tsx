"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Calendar, 
  Clock, 
  Plus, 
  Edit, 
  Trash2, 
  Play,
  Pause,
  MoreHorizontal,
  Users,
  FileText,
  AlertCircle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Filter,
  Search,
  CalendarDays,
  Timer,
  Bell,
  Settings
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import Link from "next/link"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { ParticipantsModal } from "@/components/admin/participants-modal"
import { ReminderModal } from "@/components/admin/reminder-modal"
import { EditScheduleModal } from "@/components/admin/edit-schedule-modal"
import { DeleteConfirmationDialog } from "@/components/ui/confirmation-dialog"

interface ScheduledQuiz {
  id: string
  quiz: {
    id: string
    title: string
    type: string
    difficulty: string
    questionCount: number
  }
  title: string
  description?: string
  startTime: string
  endTime: string
  duration?: number
  maxAttempts: number
  isActive: boolean
  allowLateSubmission: boolean
  showResults: boolean
  shuffleQuestions: boolean
  status: 'upcoming' | 'active' | 'completed' | 'expired'
  participants: number
  totalAttempts: number
  completedAttempts: number
  createdAt: string
  createdBy: {
    name: string
    email: string
  }
}

interface Quiz {
  id: string
  title: string
  type: string
  difficulty: string
  questionCount: number
}

interface CreateScheduledQuizData {
  quizId: string
  title: string
  description: string
  startTime: string
  endTime: string
  duration?: number
  maxAttempts: number
  allowLateSubmission: boolean
  showResults: boolean
  shuffleQuestions: boolean
}

export default function SchedulingPage() {
  const [scheduledQuizzes, setScheduledQuizzes] = useState<ScheduledQuiz[]>([])
  const [availableQuizzes, setAvailableQuizzes] = useState<Quiz[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'upcoming' | 'completed' | 'expired'>('all')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [deletingQuizzes, setDeletingQuizzes] = useState<string[]>([])

  // Modal states
  const [showParticipantsModal, setShowParticipantsModal] = useState(false)
  const [showReminderModal, setShowReminderModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedScheduledQuiz, setSelectedScheduledQuiz] = useState<ScheduledQuiz | null>(null)

  const [createForm, setCreateForm] = useState<CreateScheduledQuizData>({
    quizId: '',
    title: '',
    description: '',
    startTime: '',
    endTime: '',
    duration: undefined,
    maxAttempts: 1,
    allowLateSubmission: false,
    showResults: true,
    shuffleQuestions: false
  })

  useEffect(() => {
    fetchScheduledQuizzes()
    fetchAvailableQuizzes()
  }, [currentPage, searchQuery, statusFilter])

  const fetchScheduledQuizzes = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        status: statusFilter,
        ...(searchQuery && { search: searchQuery })
      })

      const response = await fetch(`/api/admin/scheduled-quizzes?${params}`)
      if (!response.ok) throw new Error('Failed to fetch scheduled quizzes')

      const data = await response.json()
      setScheduledQuizzes(data.data.scheduledQuizzes)
      setTotalPages(data.data.pagination.totalPages)
    } catch (error) {
      console.error('Error fetching scheduled quizzes:', error)
      toast.error('Failed to load scheduled quizzes')
    } finally {
      setLoading(false)
    }
  }

  const fetchAvailableQuizzes = async () => {
    try {
      const response = await fetch('/api/admin/quizzes?limit=100')
      if (!response.ok) throw new Error('Failed to fetch quizzes')

      const data = await response.json()

      // Ensure data.quizzes exists and is an array
      if (!data.quizzes || !Array.isArray(data.quizzes)) {
        console.error('Invalid response structure:', data)
        throw new Error('Invalid response structure from quizzes API')
      }

      const quizzes = data.quizzes.map((quiz: any) => ({
        id: quiz.id,
        title: quiz.title,
        type: quiz.type,
        difficulty: quiz.difficulty,
        questionCount: quiz.questionCount || 0
      }))
      setAvailableQuizzes(quizzes)
    } catch (error) {
      console.error('Error fetching available quizzes:', error)
      toast.error('Failed to load available quizzes')
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchScheduledQuizzes()
    setRefreshing(false)
    toast.success('Scheduled quizzes refreshed')
  }

  const handleCreateScheduledQuiz = async () => {
    if (!createForm.quizId || !createForm.title || !createForm.startTime || !createForm.endTime) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      // Convert datetime-local format to ISO string
      const formData = {
        ...createForm,
        startTime: new Date(createForm.startTime).toISOString(),
        endTime: new Date(createForm.endTime).toISOString()
      }

      const response = await fetch('/api/admin/scheduled-quizzes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || 'Failed to create scheduled quiz')
      }

      const data = await response.json()
      setScheduledQuizzes(prev => [data.data, ...prev])
      setCreateForm({
        quizId: '',
        title: '',
        description: '',
        startTime: '',
        endTime: '',
        duration: undefined,
        maxAttempts: 1,
        allowLateSubmission: false,
        showResults: true,
        shuffleQuestions: false
      })
      setShowCreateDialog(false)
      toast.success('Scheduled quiz created successfully')
    } catch (error: any) {
      console.error('Error creating scheduled quiz:', error)
      toast.error(error.message || 'Failed to create scheduled quiz')
    }
  }

  const handleDeleteScheduledQuiz = async (id: string) => {
    try {
      setDeletingQuizzes(prev => [...prev, id])
      const response = await fetch(`/api/admin/scheduled-quizzes/${id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error?.message || 'Failed to delete scheduled quiz')
      }

      setScheduledQuizzes(prev => prev.filter(quiz => quiz.id !== id))
      toast.success('Scheduled quiz deleted successfully')
    } catch (error: any) {
      console.error('Error deleting scheduled quiz:', error)
      toast.error(error.message || 'Failed to delete scheduled quiz')
    } finally {
      setDeletingQuizzes(prev => prev.filter(qId => qId !== id))
    }
  }

  const handleViewParticipants = (scheduledQuiz: ScheduledQuiz) => {
    setSelectedScheduledQuiz(scheduledQuiz)
    setShowParticipantsModal(true)
  }

  const handleSendReminder = (scheduledQuiz: ScheduledQuiz) => {
    setSelectedScheduledQuiz(scheduledQuiz)
    setShowReminderModal(true)
  }

  const handleEditSchedule = (scheduledQuiz: ScheduledQuiz) => {
    setSelectedScheduledQuiz(scheduledQuiz)
    setShowEditModal(true)
  }

  const handleScheduleUpdated = () => {
    fetchScheduledQuizzes()
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500 hover:bg-green-600'
      case 'upcoming':
        return 'bg-blue-500 hover:bg-blue-600'
      case 'completed':
        return 'bg-gray-500 hover:bg-gray-600'
      case 'expired':
        return 'bg-red-500 hover:bg-red-600'
      default:
        return 'bg-gray-500 hover:bg-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Play className="h-3 w-3" />
      case 'upcoming':
        return <Clock className="h-3 w-3" />
      case 'completed':
        return <CheckCircle className="h-3 w-3" />
      case 'expired':
        return <XCircle className="h-3 w-3" />
      default:
        return <AlertCircle className="h-3 w-3" />
    }
  }

  const handleToggleStatus = async (quizId: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'active' ? 'upcoming' : 'active'
      // API call would go here
      setScheduledQuizzes(prev => 
        prev.map(quiz => 
          quiz.id === quizId 
            ? { ...quiz, status: newStatus as any, isActive: newStatus === 'active' }
            : quiz
        )
      )
      toast.success(`Quiz ${newStatus.toLowerCase()}`)
    } catch (error) {
      toast.error('Failed to update quiz status')
    }
  }





  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quiz Scheduling</h1>
          <p className="text-muted-foreground mt-1">
            Schedule and manage quiz sessions with timing controls
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Schedule Quiz
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Schedule New Quiz</DialogTitle>
                <DialogDescription>
                  Create a scheduled quiz session with specific timing and settings
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="quiz">Select Quiz</Label>
                  <Select
                    value={createForm.quizId}
                    onValueChange={(value) => setCreateForm(prev => ({ ...prev, quizId: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a quiz to schedule" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableQuizzes.map((quiz) => (
                        <SelectItem key={quiz.id} value={quiz.id}>
                          {quiz.title} ({quiz.questionCount} questions)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="title">Schedule Title</Label>
                  <Input
                    id="title"
                    value={createForm.title}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Enter a title for this scheduled quiz"
                  />
                </div>
                <div>
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    value={createForm.description}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Enter a description for this scheduled quiz"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="startTime">Start Time</Label>
                    <Input
                      id="startTime"
                      type="datetime-local"
                      value={createForm.startTime}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, startTime: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="endTime">End Time</Label>
                    <Input
                      id="endTime"
                      type="datetime-local"
                      value={createForm.endTime}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, endTime: e.target.value }))}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="duration">Duration (minutes, optional)</Label>
                    <Input
                      id="duration"
                      type="number"
                      value={createForm.duration || ''}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, duration: e.target.value ? parseInt(e.target.value) : undefined }))}
                      placeholder="Leave empty to use quiz default"
                    />
                  </div>
                  <div>
                    <Label htmlFor="maxAttempts">Max Attempts</Label>
                    <Input
                      id="maxAttempts"
                      type="number"
                      min="1"
                      value={createForm.maxAttempts}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, maxAttempts: parseInt(e.target.value) || 1 }))}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="allowLateSubmission"
                      checked={createForm.allowLateSubmission}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, allowLateSubmission: e.target.checked }))}
                    />
                    <Label htmlFor="allowLateSubmission">Allow late submission</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="showResults"
                      checked={createForm.showResults}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, showResults: e.target.checked }))}
                    />
                    <Label htmlFor="showResults">Show results after completion</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="shuffleQuestions"
                      checked={createForm.shuffleQuestions}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, shuffleQuestions: e.target.checked }))}
                    />
                    <Label htmlFor="shuffleQuestions">Shuffle questions</Label>
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateScheduledQuiz}>
                    Schedule Quiz
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search scheduled quizzes..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as typeof statusFilter)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="upcoming">Upcoming</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Scheduled Quizzes */}
      {loading ? (
        <div className="grid gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="h-4 bg-muted rounded w-1/3"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                  <div className="h-3 bg-muted rounded w-1/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : scheduledQuizzes.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <Calendar className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No scheduled quizzes</h3>
              <p className="text-muted-foreground mb-6">
                {searchQuery || statusFilter !== 'all'
                  ? 'No quizzes match your current filters.'
                  : 'Get started by scheduling your first quiz.'}
              </p>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Schedule Your First Quiz
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {scheduledQuizzes.map((scheduledQuiz) => (
            <motion.div
              key={scheduledQuiz.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="group"
            >
              <Card className="hover:shadow-md transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold">{scheduledQuiz.title}</h3>
                        <Badge className={getStatusColor(scheduledQuiz.status)}>
                          {getStatusIcon(scheduledQuiz.status)}
                          <span className="ml-1">{scheduledQuiz.status}</span>
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-3">
                        Quiz: <span className="font-medium">{scheduledQuiz.quiz.title}</span>
                      </p>
                      
                      {scheduledQuiz.description && (
                        <p className="text-sm text-muted-foreground mb-3">
                          {scheduledQuiz.description}
                        </p>
                      )}
                      
                      <div className="flex items-center gap-6 text-sm text-muted-foreground mb-3">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>
                            {new Date(scheduledQuiz.startTime).toLocaleDateString()} - {new Date(scheduledQuiz.endTime).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <span>
                            {new Date(scheduledQuiz.startTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - {new Date(scheduledQuiz.endTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </span>
                        </div>
                        {scheduledQuiz.duration && (
                          <div className="flex items-center gap-1">
                            <Timer className="h-4 w-4" />
                            <span>{scheduledQuiz.duration} min</span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-4 text-sm">
                        <div>
                          <span className="font-medium">Participants:</span> {scheduledQuiz.participants}
                        </div>
                        <div>
                          <span className="font-medium">Completed:</span> {scheduledQuiz.completedAttempts}
                        </div>
                        <div>
                          <span className="font-medium">Max Attempts:</span> {scheduledQuiz.maxAttempts}
                        </div>
                        <div>
                          <span className="font-medium">Questions:</span> {scheduledQuiz.quiz.questionCount}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleToggleStatus(scheduledQuiz.id, scheduledQuiz.status)}
                        disabled={scheduledQuiz.status === 'completed' || scheduledQuiz.status === 'expired'}
                      >
                        {scheduledQuiz.status === 'active' ? (
                          <>
                            <Pause className="h-4 w-4 mr-1" />
                            Pause
                          </>
                        ) : (
                          <>
                            <Play className="h-4 w-4 mr-1" />
                            Activate
                          </>
                        )}
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditSchedule(scheduledQuiz)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Schedule
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleViewParticipants(scheduledQuiz)}>
                            <Users className="h-4 w-4 mr-2" />
                            View Participants
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleSendReminder(scheduledQuiz)}>
                            <Bell className="h-4 w-4 mr-2" />
                            Send Reminder
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>

                      <DeleteConfirmationDialog
                        trigger={
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-destructive hover:text-destructive"
                            disabled={deletingQuizzes.includes(scheduledQuiz.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        }
                        itemName={scheduledQuiz.title}
                        itemType="scheduled quiz"
                        onDelete={() => handleDeleteScheduledQuiz(scheduledQuiz.id)}
                        disabled={deletingQuizzes.includes(scheduledQuiz.id)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{scheduledQuizzes.length}</div>
            <p className="text-xs text-muted-foreground">Total Scheduled</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">
              {scheduledQuizzes.filter(q => q.status === 'active').length}
            </div>
            <p className="text-xs text-muted-foreground">Currently Active</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">
              {scheduledQuizzes.filter(q => q.status === 'upcoming').length}
            </div>
            <p className="text-xs text-muted-foreground">Upcoming</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">
              {scheduledQuizzes.reduce((sum, q) => sum + q.completedAttempts, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Total Attempts</p>
          </CardContent>
        </Card>
      </div>

      {/* Pagination */}
      {!loading && scheduledQuizzes.length > 0 && totalPages > 1 && (
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="text-sm text-muted-foreground">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      {/* Modals */}
      <ParticipantsModal
        open={showParticipantsModal}
        onOpenChange={setShowParticipantsModal}
        scheduledQuizId={selectedScheduledQuiz?.id || ""}
      />

      <ReminderModal
        open={showReminderModal}
        onOpenChange={setShowReminderModal}
        scheduledQuizId={selectedScheduledQuiz?.id || ""}
        scheduledQuizTitle={selectedScheduledQuiz?.title || ""}
      />

      <EditScheduleModal
        open={showEditModal}
        onOpenChange={setShowEditModal}
        scheduledQuiz={selectedScheduledQuiz}
        onScheduleUpdated={handleScheduleUpdated}
      />
    </div>
  )
}
